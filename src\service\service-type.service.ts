import { Inject, Provide } from '@midwayjs/core';
import { Service, ServiceAttributes, ServiceType } from '../entity';
import { BaseService } from '../common/BaseService';
import { Context } from '@midwayjs/koa';
import { Op, WhereOptions } from 'sequelize';

@Provide()
export class ServiceTypeService extends BaseService<ServiceType> {
  @Inject()
  ctx: Context;

  constructor() {
    super('服务类目');
  }
  getModel = () => {
    return ServiceType;
  };

  async getServicesByType(
    typeId: number,
    petInfo?: {
      type: string;
      hairType: string;
      weightType: string;
    }
  ) {
    const serviceType = await this.findById(typeId);
    if (!serviceType) {
      throw new Error('未找到指定服务类型');
    }
    const where: WhereOptions<ServiceAttributes> = {
      serviceTypeId: typeId,
      published: true, // 目前这个功能只给客户端使用，所以只返回已启用的服务
      [Op.and]: [],
    };
    const { type, hairType, weightType } = petInfo || {};
    if (type) {
      where[Op.and].push({ petTypes: type });
    }
    if (weightType) {
      where[Op.and].push({
        [Op.or]: [{ size: { [Op.is]: null } }, { size: weightType }],
      });
    }
    if (hairType) {
      where[Op.and].push({
        [Op.or]: [{ hairType: { [Op.is]: null } }, { hairType }],
      });
    }
    const services = await Service.findAll({
      where,
      attributes: [
        'id',
        'serviceName',
        'logo',
        'description',
        'basePrice',
        'petTypes',
        'size',
        'hairType',
        'distanceChargeFlag',
        'cardDiscountFlag',
        'orderIndex',
        'avgDuration', // 包含平均时长字段
      ],
    });
    return services;
  }
}
